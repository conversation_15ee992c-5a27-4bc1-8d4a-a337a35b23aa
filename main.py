import matplotlib.pyplot as plt
import networkx as nx

from collections import Counter
import common as cm
import Greedy
import time
def Degree(G,k):
    #计算平均度
    avg_degree=sum(dict(G.degree()).values())/len(G)
    degree=dict(G.degree())
    # print("所有节点的度：",degree)
    rank_degree=sorted(G.degree(),key=lambda s:s[1],reverse=True)
    # print("对度排序：",rank_degree)
    return avg_degree,rank_degree
def shortest_distance(G,k):
    #计算平均最短路径长度
    avg_shortest_distance=nx.average_shortest_path_length(G)
    # print(avg_shortest_distance)
    #计算最大最短路径长度
    max_shortest_distance=nx.diameter(G)
    # print(max_shortest_distance)
    return avg_shortest_distance,max_shortest_distance
def LLD(G,k):
    avg_degree, rank_degree, max_degree_index=[],[],[]
    C={}

    rank_degree_max=[]
    avg_degree, rank_degree=Degree(G,k)
    for i in range(0,k):
        rank_degree_max.append(rank_degree[i][1])
        max_degree_index.append(rank_degree[i][0])
        #计算每一层节点的总数
        # levels.append(nx.single_source_shortest_path_length(G,max_degree_index[i]))
        levels=nx.single_source_shortest_path_length(G,rank_degree[i][0])
        # print(levels)
        level_count=Counter(levels.values())
        # print(level_count)
        level_count_list=sorted(level_count.items())
        # print(level_count_list)
        count_rise_sum = 0
        count_decline_sum = 0
        layer_rise=0
        layer_decline=0
        for m in range(1,len(level_count)):
            if level_count_list[m][1]-level_count_list[m-1][1]>=0:
                layer_rise=layer_rise+1
                count_rise=level_count_list[m][1]-level_count_list[m-1][1]
                count_rise_sum+=count_rise
            else:
                layer_decline=layer_decline+1
                count_decline=level_count_list[m][1]-level_count_list[m-1][1]
                count_decline_sum+=count_decline
        # print(layer_rise,layer_decline)
        slope_count_rise=count_rise_sum/layer_rise
        # print("上升层节点差总数：",count_rise_sum)
        # print("上升层斜率：",slope_count_rise)
        # print("下降层节点差总数：",count_decline_sum)
        if layer_decline==0:
            slope_count_decline=0
        else:
            slope_count_decline=count_decline_sum/layer_decline
        # print("下降层斜率：",slope_count_decline)
        C_LDD=rank_degree[i][1]*slope_count_rise*layer_rise+slope_count_decline*layer_decline
        # print("LDD值：",C_LDD)
        C[rank_degree[i][0]]=C_LDD
    C_rank=sorted(C.items(),key=lambda s:s[1],reverse=True)
    # print(C)
    # print("LDD:",C_rank)
    return C_rank
# def SI(G,k,best):
#     infection_rate=0.125
#     infected_population=10
#     total_population=len(G)
#     time_step=45
#     infected_list=[k]
#     for i in range(time_step):
#         new_infected=round(infection_rate*infected_population*(total_population-infected_population))
#         infected_population+=new_infected
#         infected_list.append(infected_population)
#     return infected_list


# def inflence_photo(x0, x1, x2, x3, x4,title='netscience-int'):
#     # for i in range(19):
#     fig, ax = plt.subplots(1, 1, figsize=(6, 4))
#     plt.plot(x0[0], x0[1], c="red", marker='o', label='DC')
#     plt.plot(x1[0], x1[1], c="green", marker='*', label='BC')
#     plt.plot(x2[0], x2[1], c="blue", marker='+', label='CC')
#     plt.plot(x3[0], x3[1], c="m", marker='^', label='EC')
#     plt.plot(x4[0], x4[1],c="yellow",marker='x',label='LDD')
#     plt.xlabel('seed set size')
#     plt.ylabel('influence spread')
#     plt.legend(loc=0)
#     plt.title(title)
#     # 显示
#     plt.show()

# network_file = 'C:\\Users\\<USER>\\Desktop\\代码\\研一\\IM_DPSO\\IM_DPSO\\networks\\deezer_europe_edges.txt'
network_file = "E:\\vscodeProjects\\Python\\LDD1\\networks\\CA-GrQc-unD-int.txt"
G=nx.Graph()
edges=cm.get_txt_graph(filepath=network_file)
G.add_edges_from(edges)
m=[10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
p=0.05

# influence = []

# DC=nx.degree_centrality(G)
# DC_rank=[a[0] for a in sorted(DC.items(),key=lambda s:s[1],reverse=True)]
# print("DC:",DC_rank)
# BC=nx.betweenness_centrality(G)
# BC_rank=[a[0] for a in sorted(BC.items(),key=lambda s:s[1],reverse=True)]
# print("BC:",BC_rank)
# CC=nx.closeness_centrality(G)
# CC_rank=[a[0] for a in sorted(CC.items(),key=lambda s:s[1],reverse=True)]
# print("CC:",CC_rank)
# EC=nx.eigenvector_centrality(G)
# EC_rank=[a[0] for a in sorted(EC.items(),key=lambda s:s[1],reverse=True)]
# print("EC:",EC_rank)
# x0 , x1 , x2 , x3 , x4 = [[], []],[[], []],[[], []],[[], []],[[], []]
# for seed_k in range(k):
#     x0[0].append(seed_k)
#     x1[0].append(seed_k)
#     x2[0].append(seed_k)
#     x3[0].append(seed_k)
#     x4[0].append(seed_k)
# for seed_k in range(k):
#     x0[1].append(Greedy.IC_Algorithm(G, DC_rank[:seed_k]))
#     x1[1].append(Greedy.IC_Algorithm(G, BC_rank[:seed_k]))
#     x2[1].append(Greedy.IC_Algorithm(G, CC_rank[:seed_k]))
#     x3[1].append(Greedy.IC_Algorithm(G, EC_rank[:seed_k]))
#     x4[1].append(Greedy.IC_Algorithm(G,C_node[:seed_k]))
#
# print(x0)
# print(x1)
# print(x2)
# print(x3)
# inflence_photo(x0, x1, x2, x3,x4)
inf = []
for k in m:
    time_start = time.time()
    C_rank = LLD(G, k)
    C_node = []
    for i in range(k):
        C_rank = list(LLD(G, k))
        # print(C_rank[i][0])
        C_node.append(C_rank[i][0])
    # print("LDD:", C_node)
    G1 = Greedy.get_p_graph(network_file, p=p)
    influence = Greedy.IC_Algorithm(G1, C_node,mc=10000)  # 计算影响力的值
    print("LDD最后得出的节点集为：" + str(C_node) + "影响力大小为：" + str(influence))
    # inf.append(influence)
    endtime=time.time()-time_start
    print(endtime)
# f = [m, inf]

