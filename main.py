import matplotlib.pyplot as plt
import networkx as nx

from collections import Counter
import common as cm
import Greedy
import time
def Degree(G,k):
    #计算平均度
    avg_degree=sum(dict(G.degree()).values())/len(G)
    degree=dict(G.degree())
    # print("所有节点的度：",degree)
    rank_degree=sorted(G.degree(),key=lambda s:s[1],reverse=True)
    # print("对度排序：",rank_degree)
    return avg_degree,rank_degree
def shortest_distance(G,k):
    #计算平均最短路径长度
    avg_shortest_distance=nx.average_shortest_path_length(G)
    # print(avg_shortest_distance)
    #计算最大最短路径长度
    max_shortest_distance=nx.diameter(G)
    # print(max_shortest_distance)
    return avg_shortest_distance,max_shortest_distance
def LLD(G,k):
    avg_degree, rank_degree, max_degree_index=[],[],[]
    C={}

    rank_degree_max=[]
    avg_degree, rank_degree=Degree(G,k)
    for i in range(0,k):
        rank_degree_max.append(rank_degree[i][1])
        max_degree_index.append(rank_degree[i][0])
        #计算每一层节点的总数
        # levels.append(nx.single_source_shortest_path_length(G,max_degree_index[i]))
        levels=nx.single_source_shortest_path_length(G,rank_degree[i][0])
        # print(levels)
        level_count=Counter(levels.values())
        # print(level_count)
        level_count_list=sorted(level_count.items())
        # print(level_count_list)
        count_rise_sum = 0
        count_decline_sum = 0
        layer_rise=0
        layer_decline=0
        for m in range(1,len(level_count)):
            if level_count_list[m][1]-level_count_list[m-1][1]>=0:
                layer_rise=layer_rise+1
                count_rise=level_count_list[m][1]-level_count_list[m-1][1]
                count_rise_sum+=count_rise
            else:
                layer_decline=layer_decline+1
                count_decline=level_count_list[m][1]-level_count_list[m-1][1]
                count_decline_sum+=count_decline
        # print(layer_rise,layer_decline)
        slope_count_rise=count_rise_sum/layer_rise
        # print("上升层节点差总数：",count_rise_sum)
        # print("上升层斜率：",slope_count_rise)
        # print("下降层节点差总数：",count_decline_sum)
        if layer_decline==0:
            slope_count_decline=0
        else:
            slope_count_decline=count_decline_sum/layer_decline
        # print("下降层斜率：",slope_count_decline)
        C_LDD=rank_degree[i][1]*slope_count_rise*layer_rise+slope_count_decline*layer_decline
        # print("LDD值：",C_LDD)
        C[rank_degree[i][0]]=C_LDD
    C_rank=sorted(C.items(),key=lambda s:s[1],reverse=True)
    # print(C)
    # print("LDD:",C_rank)
    return C_rank
# def SI(G,k,best):
#     infection_rate=0.125
#     infected_population=10
#     total_population=len(G)
#     time_step=45
#     infected_list=[k]
#     for i in range(time_step):
#         new_infected=round(infection_rate*infected_population*(total_population-infected_population))
#         infected_population+=new_infected
#         infected_list.append(infected_population)
#     return infected_list


# def inflence_photo(x0, x1, x2, x3, x4,title='netscience-int'):
#     # for i in range(19):
#     fig, ax = plt.subplots(1, 1, figsize=(6, 4))
#     plt.plot(x0[0], x0[1], c="red", marker='o', label='DC')
#     plt.plot(x1[0], x1[1], c="green", marker='*', label='BC')
#     plt.plot(x2[0], x2[1], c="blue", marker='+', label='CC')
#     plt.plot(x3[0], x3[1], c="m", marker='^', label='EC')
#     plt.plot(x4[0], x4[1],c="yellow",marker='x',label='LDD')
#     plt.xlabel('seed set size')
#     plt.ylabel('influence spread')
#     plt.legend(loc=0)
#     plt.title(title)
#     # 显示
#     plt.show()

# network_file = 'C:\\Users\\<USER>\\Desktop\\代码\\研一\\IM_DPSO\\IM_DPSO\\networks\\deezer_europe_edges.txt'
network_file = "networks/CA-GrQc-unD-int.txt"
G=nx.Graph()
edges=cm.get_txt_graph(filepath=network_file)
G.add_edges_from(edges)

# 分析网络基本特性
print("=== CA-GrQc网络基本特性分析 ===")
print(f"节点数: {G.number_of_nodes()}")
print(f"边数: {G.number_of_edges()}")
print(f"平均度: {2*G.number_of_edges()/G.number_of_nodes():.4f}")
print(f"密度: {nx.density(G):.6f}")

# 计算度分布
degree_sequence = [d for n, d in G.degree()]
print(f"最大度: {max(degree_sequence)}")
print(f"最小度: {min(degree_sequence)}")
print(f"度的标准差: {np.std(degree_sequence):.4f}")

# 分析连通性
if nx.is_connected(G):
    print("网络是连通的")
    print(f"直径: {nx.diameter(G)}")
    print(f"平均路径长度: {nx.average_shortest_path_length(G):.4f}")
else:
    print("网络不连通")
    components = list(nx.connected_components(G))
    print(f"连通分量数: {len(components)}")
    largest_cc = max(components, key=len)
    print(f"最大连通分量大小: {len(largest_cc)}")

# 分析聚类系数
print(f"平均聚类系数: {nx.average_clustering(G):.4f}")

print("\n=== LDD算法详细分析 ===")

def analyze_LDD_selection(G, k_values):
    """分析LDD算法在不同k值下的节点选择模式"""
    results = {}

    for k in k_values:
        print(f"\n--- 分析 k={k} ---")
        C_rank = LLD(G, k)
        selected_nodes = [node for node, score in C_rank[:k]]

        # 分析选中节点的度
        selected_degrees = [G.degree(node) for node in selected_nodes]
        print(f"选中节点的度: {selected_degrees}")
        print(f"选中节点平均度: {np.mean(selected_degrees):.2f}")
        print(f"选中节点度的标准差: {np.std(selected_degrees):.2f}")

        # 分析节点间的重叠
        if k > 10:
            prev_k = k - 10
            if prev_k in results:
                prev_nodes = set(results[prev_k]['nodes'])
                curr_nodes = set(selected_nodes)
                overlap = len(prev_nodes.intersection(curr_nodes))
                new_nodes = len(curr_nodes - prev_nodes)
                print(f"与k={prev_k}的重叠节点数: {overlap}")
                print(f"新增节点数: {new_nodes}")
                print(f"重叠率: {overlap/prev_k:.2%}")

        # 分析选中节点的LDD分数
        ldd_scores = [score for node, score in C_rank[:k]]
        print(f"LDD分数范围: {min(ldd_scores):.2f} - {max(ldd_scores):.2f}")
        print(f"LDD分数标准差: {np.std(ldd_scores):.2f}")

        results[k] = {
            'nodes': selected_nodes,
            'degrees': selected_degrees,
            'ldd_scores': ldd_scores
        }

    return results

# 分析不同k值下的选择模式
k_values = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
ldd_analysis = analyze_LDD_selection(G, k_values)

print("\n=== 影响力传播分析 ===")
p = 0.05
inf_results = []

for k in k_values:
    time_start = time.time()
    C_rank = LLD(G, k)
    C_node = [node for node, score in C_rank[:k]]

    G1 = Greedy.get_p_graph(network_file, p=p)
    influence = Greedy.IC_Algorithm(G1, C_node, mc=1000)  # 减少MC次数以加快分析
    inf_results.append((k, influence))

    print(f"k={k}: 影响力={influence:.4f}")

    # 计算边际增益
    if len(inf_results) > 1:
        prev_inf = inf_results[-2][1]
        marginal_gain = influence - prev_inf
        print(f"  边际增益: {marginal_gain:.4f}")

    endtime = time.time() - time_start
    print(f"  计算时间: {endtime:.2f}s")

print("\n=== 影响力增益分析 ===")
for i in range(1, len(inf_results)):
    k_curr, inf_curr = inf_results[i]
    k_prev, inf_prev = inf_results[i-1]
    marginal_gain = inf_curr - inf_prev
    gain_per_node = marginal_gain / (k_curr - k_prev)
    print(f"k={k_prev}→{k_curr}: 边际增益={marginal_gain:.4f}, 每节点增益={gain_per_node:.4f}")

# 找出增益突变点
print("\n=== 增益突变点分析 ===")
gains = []
for i in range(1, len(inf_results)):
    k_curr, inf_curr = inf_results[i]
    k_prev, inf_prev = inf_results[i-1]
    marginal_gain = inf_curr - inf_prev
    gains.append((k_curr, marginal_gain))

avg_gain = np.mean([gain for k, gain in gains])
std_gain = np.std([gain for k, gain in gains])
print(f"平均边际增益: {avg_gain:.4f}")
print(f"边际增益标准差: {std_gain:.4f}")

print("异常增益点:")
for k, gain in gains:
    if gain > avg_gain + std_gain:
        print(f"  k={k}: 增益={gain:.4f} (高于平均值{(gain-avg_gain)/std_gain:.1f}个标准差)")
    elif gain < avg_gain - std_gain:
        print(f"  k={k}: 增益={gain:.4f} (低于平均值{abs(gain-avg_gain)/std_gain:.1f}个标准差)")

