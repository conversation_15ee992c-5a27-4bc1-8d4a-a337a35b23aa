import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from collections import Counter, defaultdict
import common as cm
import Greedy
from main import LLD

def analyze_network_structure(G):
    """深入分析网络结构特性"""
    print("=== 深度网络结构分析 ===")
    
    # 度分布分析
    degrees = [d for n, d in G.degree()]
    degree_count = Counter(degrees)
    
    print(f"度分布统计:")
    print(f"  度为1的节点数: {degree_count[1]} ({degree_count[1]/len(G)*100:.1f}%)")
    print(f"  度为2-5的节点数: {sum(degree_count[d] for d in range(2,6))} ({sum(degree_count[d] for d in range(2,6))/len(G)*100:.1f}%)")
    print(f"  度为6-10的节点数: {sum(degree_count[d] for d in range(6,11))} ({sum(degree_count[d] for d in range(6,11))/len(G)*100:.1f}%)")
    print(f"  度>10的节点数: {sum(degree_count[d] for d in range(11,max(degrees)+1))} ({sum(degree_count[d] for d in range(11,max(degrees)+1))/len(G)*100:.1f}%)")
    
    # 高度节点分析
    high_degree_nodes = [n for n, d in G.degree() if d >= 30]
    print(f"\n高度节点(度>=30)分析:")
    print(f"  数量: {len(high_degree_nodes)}")
    print(f"  占比: {len(high_degree_nodes)/len(G)*100:.2f}%")
    
    # 分析高度节点之间的连接
    high_degree_subgraph = G.subgraph(high_degree_nodes)
    print(f"  高度节点间边数: {high_degree_subgraph.number_of_edges()}")
    print(f"  高度节点间密度: {nx.density(high_degree_subgraph):.4f}")
    
    return high_degree_nodes, degree_count

def analyze_node_overlap_pattern(G, k_values):
    """分析节点重叠模式和网络覆盖"""
    print("\n=== 节点重叠和网络覆盖分析 ===")
    
    all_selected_nodes = set()
    coverage_analysis = {}
    
    for k in k_values:
        C_rank = LLD(G, k)
        selected_nodes = set([node for node, score in C_rank[:k]])
        
        # 计算累积覆盖
        all_selected_nodes.update(selected_nodes)
        
        # 分析这些节点的邻居覆盖
        neighbors_covered = set()
        for node in selected_nodes:
            neighbors_covered.update(G.neighbors(node))
            neighbors_covered.add(node)  # 包括节点自身
        
        coverage_analysis[k] = {
            'selected_nodes': selected_nodes,
            'neighbors_covered': neighbors_covered,
            'coverage_ratio': len(neighbors_covered) / len(G)
        }
        
        print(f"k={k}:")
        print(f"  直接覆盖节点数: {len(neighbors_covered)}")
        print(f"  网络覆盖率: {len(neighbors_covered)/len(G)*100:.1f}%")
        
        if k > k_values[0]:
            prev_k = k_values[k_values.index(k)-1]
            prev_coverage = coverage_analysis[prev_k]['neighbors_covered']
            new_coverage = neighbors_covered - prev_coverage
            print(f"  新增覆盖节点: {len(new_coverage)}")
            print(f"  覆盖增长率: {len(new_coverage)/(k-prev_k):.1f} 节点/种子")
    
    return coverage_analysis

def analyze_ldd_score_distribution(G, k_values):
    """分析LDD分数分布和选择策略"""
    print("\n=== LDD分数分布分析 ===")
    
    # 计算所有节点的LDD分数
    all_ldd_scores = LLD(G, len(G))
    scores = [score for node, score in all_ldd_scores]
    
    print(f"全网LDD分数统计:")
    print(f"  最大值: {max(scores):.0f}")
    print(f"  最小值: {min(scores):.0f}")
    print(f"  平均值: {np.mean(scores):.0f}")
    print(f"  中位数: {np.median(scores):.0f}")
    print(f"  标准差: {np.std(scores):.0f}")
    
    # 分析分数分布的分位数
    percentiles = [90, 95, 99, 99.5, 99.9]
    for p in percentiles:
        threshold = np.percentile(scores, p)
        count = sum(1 for s in scores if s >= threshold)
        print(f"  {p}%分位数: {threshold:.0f} (有{count}个节点)")
    
    # 分析不同k值下的分数阈值
    print(f"\n不同k值的LDD分数阈值:")
    for k in k_values:
        C_rank = LLD(G, k)
        if k <= len(C_rank):
            min_score = C_rank[k-1][1]
            print(f"  k={k}: 最低分数={min_score:.0f}")

def analyze_marginal_gain_pattern(G, k_values):
    """分析边际增益模式"""
    print("\n=== 边际增益深度分析 ===")
    
    p = 0.05
    influences = []
    
    for k in k_values:
        C_rank = LLD(G, k)
        C_node = [node for node, score in C_rank[:k]]
        
        G1 = Greedy.get_p_graph("networks/CA-GrQc-unD-int.txt", p=p)
        influence = Greedy.IC_Algorithm(G1, C_node, mc=500)
        influences.append(influence)
    
    # 分析增益模式
    gains = []
    for i in range(1, len(influences)):
        gain = influences[i] - influences[i-1]
        gains.append(gain)
        
    print("边际增益详细分析:")
    for i, gain in enumerate(gains):
        k_from = k_values[i]
        k_to = k_values[i+1]
        print(f"  k={k_from}→{k_to}: 增益={gain:.4f}")
        
        # 分析增益异常的原因
        if i > 0:
            prev_gain = gains[i-1]
            gain_change = gain - prev_gain
            if abs(gain_change) > 5:  # 增益变化超过5
                print(f"    *** 增益异常变化: {gain_change:+.4f} ***")
                
                # 分析新增节点特性
                C_rank_from = LLD(G, k_from)
                C_rank_to = LLD(G, k_to)
                nodes_from = set([node for node, score in C_rank_from[:k_from]])
                nodes_to = set([node for node, score in C_rank_to[:k_to]])
                new_nodes = nodes_to - nodes_from
                
                print(f"    新增节点: {list(new_nodes)}")
                new_degrees = [G.degree(node) for node in new_nodes]
                print(f"    新增节点度: {new_degrees}")
                print(f"    新增节点平均度: {np.mean(new_degrees):.1f}")
    
    return influences, gains

def create_visualization(G, k_values, coverage_analysis):
    """创建可视化图表"""
    print("\n=== 创建可视化分析 ===")
    
    # 1. 覆盖率增长图
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 3, 1)
    coverage_ratios = [coverage_analysis[k]['coverage_ratio'] for k in k_values]
    plt.plot(k_values, coverage_ratios, 'bo-')
    plt.xlabel('种子集大小 k')
    plt.ylabel('网络覆盖率')
    plt.title('网络覆盖率随k的变化')
    plt.grid(True)
    
    # 2. 边际覆盖增益
    plt.subplot(2, 3, 2)
    marginal_coverage = []
    for i in range(1, len(k_values)):
        curr_coverage = len(coverage_analysis[k_values[i]]['neighbors_covered'])
        prev_coverage = len(coverage_analysis[k_values[i-1]]['neighbors_covered'])
        marginal = curr_coverage - prev_coverage
        marginal_coverage.append(marginal)
    
    plt.plot(k_values[1:], marginal_coverage, 'ro-')
    plt.xlabel('种子集大小 k')
    plt.ylabel('边际覆盖增益')
    plt.title('边际覆盖增益')
    plt.grid(True)
    
    # 3. 度分布
    plt.subplot(2, 3, 3)
    degrees = [d for n, d in G.degree()]
    plt.hist(degrees, bins=50, alpha=0.7)
    plt.xlabel('节点度')
    plt.ylabel('频数')
    plt.title('度分布')
    plt.yscale('log')
    
    # 4. 选中节点度的变化
    plt.subplot(2, 3, 4)
    avg_degrees = []
    for k in k_values:
        C_rank = LLD(G, k)
        selected_nodes = [node for node, score in C_rank[:k]]
        avg_degree = np.mean([G.degree(node) for node in selected_nodes])
        avg_degrees.append(avg_degree)
    
    plt.plot(k_values, avg_degrees, 'go-')
    plt.xlabel('种子集大小 k')
    plt.ylabel('选中节点平均度')
    plt.title('选中节点平均度变化')
    plt.grid(True)
    
    # 5. LDD分数分布
    plt.subplot(2, 3, 5)
    all_ldd_scores = LLD(G, len(G))
    scores = [score for node, score in all_ldd_scores]
    plt.hist(scores, bins=50, alpha=0.7)
    plt.xlabel('LDD分数')
    plt.ylabel('频数')
    plt.title('LDD分数分布')
    plt.yscale('log')
    
    # 6. 影响力增长曲线
    plt.subplot(2, 3, 6)
    influences, gains = analyze_marginal_gain_pattern(G, k_values)
    plt.plot(k_values, influences, 'mo-')
    plt.xlabel('种子集大小 k')
    plt.ylabel('影响力')
    plt.title('影响力增长曲线')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('ca_grqc_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 加载网络
    network_file = "networks/CA-GrQc-unD-int.txt"
    G = nx.Graph()
    edges = cm.get_txt_graph(filepath=network_file)
    G.add_edges_from(edges)
    
    k_values = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    
    # 执行各种分析
    high_degree_nodes, degree_count = analyze_network_structure(G)
    coverage_analysis = analyze_node_overlap_pattern(G, k_values)
    analyze_ldd_score_distribution(G, k_values)
    
    # 创建可视化
    create_visualization(G, k_values, coverage_analysis)
    
    print("\n=== 总结分析 ===")
    print("CA-GrQc网络的异常增益模式可能原因:")
    print("1. 网络具有明显的度异质性，少数高度节点主导网络结构")
    print("2. LDD算法在k=70之前主要选择高度节点，但这些节点间存在大量重叠")
    print("3. k=80时开始选择一些中等度数但位置更优的节点，减少了重叠效应")
    print("4. 网络的聚类结构导致前期选择的节点影响范围重叠严重")
    print("5. 后期选择的节点能够触达之前未覆盖的网络区域，产生突然的增益提升")
