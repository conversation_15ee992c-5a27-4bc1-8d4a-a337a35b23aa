
import networkx as nx
import numpy as np
import copy

def IC_Algorithm(G, S_set, mc):
    s_influence = 0
    count = 0
    activated_node = {}
    for node in G.nodes():
        activated_node[node] = 0

    # -------------- 蒙特卡洛模拟 -------------- #
    for m in range(mc):
        temp_s = copy.deepcopy(S_set)
        s_state = []
        s_state = copy.deepcopy(list(S_set))  # 表示已经激活的节点
        new_activate_set = set()
        new_activate = True
        while new_activate:
            for v in temp_s:
                for v_neighbor in G.neighbors(v):
                    if v_neighbor not in s_state:
                        ran = np.random.random()
                        if (ran < G[v][v_neighbor]['probability']):
                            s_state.append(v_neighbor)
                            new_activate_set.add(v_neighbor)
            # count += len(new_activate_set)
            if new_activate_set:
                temp_s = list(new_activate_set)
                new_activate_set = set()
            else:
                new_activate = False
        count += len(s_state)
        for activated in s_state:
            activated_node[activated] = activated_node[activated] + 1

    s_influence = count / mc
    return s_influence


def get_p_graph(filename, p):
    edges_data = np.loadtxt(filename, usecols=[0, 1], skiprows=1, dtype=int)
    edges_list = [tuple(line) for line in edges_data]
    G = nx.Graph()
    G.add_edges_from(edges_list)
    for v in G.nodes():
        for v_neighbor in G.neighbors(v):
            if v != v_neighbor:
                G.add_edge(v, v_neighbor, probability=p)

    return G
