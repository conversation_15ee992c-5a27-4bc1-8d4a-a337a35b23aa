import networkx as nx
import numpy as np
from collections import Counter
import common as cm
import os

def analyze_network_characteristics():
    """详细分析所有网络的特征"""
    
    networks = [
        "AS733.txt",
        "blog-int.txt", 
        "CA-GrQc-unD-int.txt",
        "CA-HepTh.txt",
        "deezer.txt",
        "lastfm.txt", 
        "NetHEHT.txt",
        "twin_zero_indexed.txt"
    ]
    
    network_data = {}
    
    for network_file in networks:
        filepath = f"networks/{network_file}"
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            continue
            
        print(f"\n正在分析网络: {network_file}")
        
        try:
            # 加载网络
            G = nx.Graph()
            edges = cm.get_txt_graph(filepath=filepath)
            G.add_edges_from(edges)
            
            # 基本统计
            n_nodes = G.number_of_nodes()
            n_edges = G.number_of_edges()
            
            if n_nodes == 0:
                print(f"网络 {network_file} 没有节点")
                continue
                
            # 度分布分析
            degrees = [d for n, d in G.degree()]
            avg_degree = np.mean(degrees)
            std_degree = np.std(degrees)
            max_degree = max(degrees)
            min_degree = min(degrees)
            
            # 度异质性指标
            degree_heterogeneity = std_degree / avg_degree if avg_degree > 0 else 0
            degree_variance = np.var(degrees)
            
            # 度分布的偏度和峰度
            from scipy import stats
            degree_skewness = stats.skew(degrees)
            degree_kurtosis = stats.kurtosis(degrees)
            
            # 网络密度
            density = nx.density(G)
            
            # 聚类系数
            try:
                avg_clustering = nx.average_clustering(G)
            except:
                avg_clustering = 0
            
            # 连通性分析
            is_connected = nx.is_connected(G)
            if is_connected:
                diameter = nx.diameter(G)
                avg_path_length = nx.average_shortest_path_length(G)
                components = 1
                largest_cc_size = n_nodes
            else:
                components = list(nx.connected_components(G))
                n_components = len(components)
                largest_cc = max(components, key=len)
                largest_cc_size = len(largest_cc)
                
                # 在最大连通分量上计算路径长度
                largest_cc_graph = G.subgraph(largest_cc)
                try:
                    diameter = nx.diameter(largest_cc_graph)
                    avg_path_length = nx.average_shortest_path_length(largest_cc_graph)
                except:
                    diameter = 0
                    avg_path_length = 0
                components = n_components
            
            # 度中心性分析
            degree_centrality = nx.degree_centrality(G)
            max_degree_centrality = max(degree_centrality.values())
            
            # 计算度分布的基尼系数（另一个异质性指标）
            sorted_degrees = sorted(degrees)
            n = len(sorted_degrees)
            cumsum = np.cumsum(sorted_degrees)
            gini_coefficient = (n + 1 - 2 * sum((n + 1 - i) * y for i, y in enumerate(cumsum))) / (n * sum(sorted_degrees))
            
            # 幂律拟合检验
            try:
                import powerlaw
                fit = powerlaw.Fit(degrees, discrete=True)
                alpha = fit.power_law.alpha
                xmin = fit.power_law.xmin
                power_law_likelihood = fit.power_law.loglikelihoods(degrees)[0]
            except:
                alpha = None
                xmin = None
                power_law_likelihood = None
            
            # 小世界特性
            # 计算随机网络的聚类系数和路径长度作为对比
            try:
                random_clustering = avg_degree / (n_nodes - 1)
                if is_connected:
                    small_world_sigma = (avg_clustering / random_clustering) / (avg_path_length / np.log(n_nodes))
                else:
                    small_world_sigma = None
            except:
                small_world_sigma = None
            
            # 度分布统计
            degree_counts = Counter(degrees)
            high_degree_nodes = sum(1 for d in degrees if d >= avg_degree + 2*std_degree)
            low_degree_nodes = sum(1 for d in degrees if d <= 2)
            
            # 存储分析结果
            network_data[network_file] = {
                'basic_stats': {
                    'nodes': n_nodes,
                    'edges': n_edges,
                    'avg_degree': avg_degree,
                    'density': density
                },
                'degree_distribution': {
                    'max_degree': max_degree,
                    'min_degree': min_degree,
                    'std_degree': std_degree,
                    'degree_variance': degree_variance,
                    'degree_skewness': degree_skewness,
                    'degree_kurtosis': degree_kurtosis
                },
                'heterogeneity': {
                    'degree_heterogeneity': degree_heterogeneity,
                    'gini_coefficient': gini_coefficient,
                    'max_degree_centrality': max_degree_centrality
                },
                'structure': {
                    'avg_clustering': avg_clustering,
                    'diameter': diameter,
                    'avg_path_length': avg_path_length,
                    'components': components,
                    'largest_cc_ratio': largest_cc_size / n_nodes,
                    'small_world_sigma': small_world_sigma
                },
                'power_law': {
                    'alpha': alpha,
                    'xmin': xmin,
                    'likelihood': power_law_likelihood
                },
                'node_distribution': {
                    'high_degree_nodes': high_degree_nodes,
                    'low_degree_nodes': low_degree_nodes,
                    'high_degree_ratio': high_degree_nodes / n_nodes,
                    'low_degree_ratio': low_degree_nodes / n_nodes
                }
            }
            
            print(f"完成分析: {network_file}")
            
        except Exception as e:
            print(f"分析 {network_file} 时出错: {e}")
            continue
    
    return network_data

def rank_networks_by_heterogeneity(network_data):
    """根据异质性对网络进行排名"""
    
    heterogeneity_metrics = {}
    
    for network, data in network_data.items():
        # 综合异质性评分
        degree_het = data['heterogeneity']['degree_heterogeneity']
        gini = data['heterogeneity']['gini_coefficient']
        max_centrality = data['heterogeneity']['max_degree_centrality']
        
        # 标准化后的综合评分
        composite_score = (degree_het + gini + max_centrality) / 3
        
        heterogeneity_metrics[network] = {
            'degree_heterogeneity': degree_het,
            'gini_coefficient': gini,
            'max_degree_centrality': max_centrality,
            'composite_score': composite_score
        }
    
    # 按综合评分排序
    ranked_networks = sorted(heterogeneity_metrics.items(), 
                           key=lambda x: x[1]['composite_score'], 
                           reverse=True)
    
    return ranked_networks, heterogeneity_metrics

if __name__ == "__main__":
    print("开始网络特征分析...")
    network_data = analyze_network_characteristics()
    
    print("\n计算异质性排名...")
    ranked_networks, heterogeneity_metrics = rank_networks_by_heterogeneity(network_data)
    
    print("\n异质性排名:")
    for i, (network, metrics) in enumerate(ranked_networks, 1):
        print(f"{i}. {network}: 综合评分={metrics['composite_score']:.4f}")
    
    # 保存详细分析结果
    import json
    with open('network_analysis_results.json', 'w') as f:
        json.dump(network_data, f, indent=2)
    
    print("\n分析完成，结果已保存到 network_analysis_results.json")
