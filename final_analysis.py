import networkx as nx
import numpy as np
from collections import Counter, defaultdict
import common as cm
import Greedy
from main import LLD

def comprehensive_analysis():
    """CA-GrQc网络LDD算法异常增益的综合分析"""
    
    # 加载网络
    network_file = "networks/CA-GrQc-unD-int.txt"
    G = nx.Graph()
    edges = cm.get_txt_graph(filepath=network_file)
    G.add_edges_from(edges)
    
    print("CA-GrQc网络LDD算法异常增益深度分析报告")
    print("=" * 60)
    
    # 1. 网络基本特性
    print("\n1. 网络基本特性:")
    print(f"   节点数: {G.number_of_nodes()}")
    print(f"   边数: {G.number_of_edges()}")
    print(f"   平均度: {2*G.number_of_edges()/G.number_of_nodes():.2f}")
    print(f"   密度: {nx.density(G):.6f}")
    print(f"   平均聚类系数: {nx.average_clustering(G):.4f}")
    
    # 度分布分析
    degrees = [d for n, d in G.degree()]
    print(f"   度分布: 最大={max(degrees)}, 最小={min(degrees)}, 标准差={np.std(degrees):.2f}")
    
    # 连通性分析
    components = list(nx.connected_components(G))
    largest_cc = max(components, key=len)
    print(f"   连通分量: {len(components)}个, 最大分量={len(largest_cc)}节点({len(largest_cc)/len(G)*100:.1f}%)")
    
    # 2. 关键发现：k=80突破点分析
    print("\n2. 关键发现：k=80突破点分析")
    
    k70_nodes = set([node for node, score in LLD(G, 70)[:70]])
    k80_nodes = set([node for node, score in LLD(G, 80)[:80]])
    new_nodes_80 = k80_nodes - k70_nodes
    
    print(f"   k=80新增的10个节点: {sorted(list(new_nodes_80))}")
    
    # 分析新节点特性
    new_degrees = [G.degree(node) for node in new_nodes_80]
    new_clustering = [nx.clustering(G, node) for node in new_nodes_80]
    
    print(f"   新增节点度分布: {new_degrees}")
    print(f"   新增节点平均度: {np.mean(new_degrees):.1f} (vs k=70平均度: 50.2)")
    print(f"   新增节点聚类系数: 平均={np.mean(new_clustering):.3f}, 范围=[{min(new_clustering):.3f}, {max(new_clustering):.3f}]")
    
    # 3. 覆盖分析
    print("\n3. 网络覆盖效应分析:")
    
    # k=70的覆盖
    k70_coverage = set()
    for node in k70_nodes:
        k70_coverage.update(G.neighbors(node))
        k70_coverage.add(node)
    
    # k=80新增节点的覆盖
    new_coverage = set()
    for node in new_nodes_80:
        new_coverage.update(G.neighbors(node))
        new_coverage.add(node)
    
    overlap_coverage = new_coverage.intersection(k70_coverage)
    unique_coverage = new_coverage - k70_coverage
    
    print(f"   k=70总覆盖: {len(k70_coverage)}节点 ({len(k70_coverage)/len(G)*100:.1f}%)")
    print(f"   k=80新增覆盖: {len(new_coverage)}节点")
    print(f"   重叠覆盖: {len(overlap_coverage)}节点")
    print(f"   独特覆盖: {len(unique_coverage)}节点 ({len(unique_coverage)/len(new_coverage)*100:.1f}%)")
    
    # 4. 影响力增益分析
    print("\n4. 影响力增益模式分析:")
    
    k_values = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    p = 0.05
    influences = []
    
    for k in k_values:
        C_rank = LLD(G, k)
        C_node = [node for node, score in C_rank[:k]]
        G1 = Greedy.get_p_graph(network_file, p=p)
        influence = Greedy.IC_Algorithm(G1, C_node, mc=500)
        influences.append(influence)
    
    # 计算边际增益
    gains = []
    for i in range(1, len(influences)):
        gain = influences[i] - influences[i-1]
        gains.append(gain)
        k_from, k_to = k_values[i-1], k_values[i]
        print(f"   k={k_from}→{k_to}: 影响力={influences[i]:.1f}, 边际增益={gain:.1f}")
    
    # 5. 异常增益原因分析
    print("\n5. 异常增益原因深度分析:")
    
    # 分析k=30-70期间的低增益
    low_gain_period = gains[2:7]  # k=30-70
    high_gain_k80 = gains[7]     # k=70-80
    
    print(f"   k=30-70期间平均增益: {np.mean(low_gain_period):.1f}")
    print(f"   k=80突破增益: {high_gain_k80:.1f} (是平均值的{high_gain_k80/np.mean(low_gain_period):.1f}倍)")
    
    # 分析节点选择模式
    print("\n   节点选择模式变化:")
    for k in [30, 50, 70, 80]:
        selected_nodes = [node for node, score in LLD(G, k)[:k]]
        avg_degree = np.mean([G.degree(node) for node in selected_nodes])
        avg_clustering = np.mean([nx.clustering(G, node) for node in selected_nodes])
        
        # 计算内部连接密度
        subgraph = G.subgraph(selected_nodes)
        internal_density = nx.density(subgraph)
        
        print(f"   k={k}: 平均度={avg_degree:.1f}, 平均聚类={avg_clustering:.3f}, 内部密度={internal_density:.3f}")
    
    # 6. 网络结构对算法的影响
    print("\n6. 网络结构特性对LDD算法的影响:")
    
    # 高度节点分析
    high_degree_nodes = [n for n, d in G.degree() if d >= 40]
    print(f"   高度节点(度≥40): {len(high_degree_nodes)}个 ({len(high_degree_nodes)/len(G)*100:.2f}%)")
    
    # 分析高度节点的聚类
    high_degree_clustering = [nx.clustering(G, node) for node in high_degree_nodes]
    print(f"   高度节点平均聚类系数: {np.mean(high_degree_clustering):.3f}")
    
    # 分析高度节点间的连接
    high_degree_subgraph = G.subgraph(high_degree_nodes)
    print(f"   高度节点间连接密度: {nx.density(high_degree_subgraph):.3f}")
    
    # 7. 论文解释建议
    print("\n7. 论文写作建议:")
    print("   异常增益模式的主要原因:")
    print("   (1) 网络度异质性: 少数高度节点(2.8%)主导早期选择")
    print("   (2) 高聚类效应: 平均聚类系数0.53导致局部影响力饱和")
    print("   (3) 邻居重叠严重: k=30-70期间新增覆盖率极低(0.3-1.7节点/种子)")
    print("   (4) 结构突破点: k=80时选择的中等度节点(平均度38.6)具有更好的桥接作用")
    print("   (5) 覆盖效率提升: k=80新增节点70.9%为独特覆盖,显著提高传播效率")
    
    print("\n   建议的解释框架:")
    print("   - 强调CA-GrQc作为合作网络的高聚类特性")
    print("   - 说明LDD算法在高聚类网络中的'饱和效应'")
    print("   - 解释为什么需要足够多的种子才能突破局部聚类限制")
    print("   - 讨论度中心性与网络位置重要性的权衡")
    
    # 8. 与其他网络的对比建议
    print("\n8. 与其他网络对比的建议:")
    print("   建议对比分析:")
    print("   - 选择低聚类系数的网络(如随机网络)作为对照")
    print("   - 比较不同聚类系数网络中LDD算法的增益模式")
    print("   - 分析网络密度对算法性能的影响")
    print("   - 研究连通分量数量对影响力传播的影响")

if __name__ == "__main__":
    comprehensive_analysis()
