import networkx as nx
import numpy as np
from collections import Counter
import common as cm
import os

def analyze_single_network(network_file):
    """分析单个网络的特征"""
    
    filepath = f"networks/{network_file}"
    if not os.path.exists(filepath):
        return None
        
    print(f"\n=== 分析网络: {network_file} ===")
    
    try:
        # 加载网络
        G = nx.Graph()
        edges = cm.get_txt_graph(filepath=filepath)
        G.add_edges_from(edges)
        
        # 基本统计
        n_nodes = G.number_of_nodes()
        n_edges = G.number_of_edges()
        
        if n_nodes == 0:
            return None
            
        # 度分布分析
        degrees = [d for n, d in G.degree()]
        avg_degree = np.mean(degrees)
        std_degree = np.std(degrees)
        max_degree = max(degrees)
        min_degree = min(degrees)
        
        # 度异质性指标
        degree_heterogeneity = std_degree / avg_degree if avg_degree > 0 else 0
        
        # 网络密度
        density = nx.density(G)
        
        # 聚类系数
        avg_clustering = nx.average_clustering(G)
        
        # 连通性分析
        is_connected = nx.is_connected(G)
        if is_connected:
            diameter = nx.diameter(G)
            avg_path_length = nx.average_shortest_path_length(G)
            n_components = 1
            largest_cc_ratio = 1.0
        else:
            components = list(nx.connected_components(G))
            n_components = len(components)
            largest_cc = max(components, key=len)
            largest_cc_ratio = len(largest_cc) / n_nodes
            
            # 在最大连通分量上计算
            largest_cc_graph = G.subgraph(largest_cc)
            if len(largest_cc) > 1:
                diameter = nx.diameter(largest_cc_graph)
                avg_path_length = nx.average_shortest_path_length(largest_cc_graph)
            else:
                diameter = 0
                avg_path_length = 0
        
        # 度分布统计
        degree_counts = Counter(degrees)
        
        # 计算度分布的偏度（衡量异质性）
        degree_skewness = np.mean([(d - avg_degree)**3 for d in degrees]) / (std_degree**3) if std_degree > 0 else 0
        
        # 高度节点比例
        high_degree_threshold = avg_degree + 2 * std_degree
        high_degree_nodes = sum(1 for d in degrees if d >= high_degree_threshold)
        high_degree_ratio = high_degree_nodes / n_nodes
        
        # 低度节点比例
        low_degree_nodes = sum(1 for d in degrees if d <= 2)
        low_degree_ratio = low_degree_nodes / n_nodes
        
        # 度中心性
        max_degree_centrality = max_degree / (n_nodes - 1) if n_nodes > 1 else 0
        
        # 简化的基尼系数计算
        sorted_degrees = sorted(degrees)
        n = len(sorted_degrees)
        if sum(sorted_degrees) > 0:
            cumulative_sum = np.cumsum(sorted_degrees)
            gini = (2 * sum((i + 1) * degree for i, degree in enumerate(sorted_degrees))) / (n * sum(sorted_degrees)) - (n + 1) / n
        else:
            gini = 0
        
        result = {
            'network_file': network_file,
            'basic_stats': {
                'nodes': n_nodes,
                'edges': n_edges,
                'avg_degree': avg_degree,
                'density': density
            },
            'degree_stats': {
                'max_degree': max_degree,
                'min_degree': min_degree,
                'std_degree': std_degree,
                'degree_skewness': degree_skewness
            },
            'heterogeneity': {
                'degree_heterogeneity': degree_heterogeneity,
                'gini_coefficient': gini,
                'max_degree_centrality': max_degree_centrality,
                'high_degree_ratio': high_degree_ratio,
                'low_degree_ratio': low_degree_ratio
            },
            'structure': {
                'avg_clustering': avg_clustering,
                'diameter': diameter,
                'avg_path_length': avg_path_length,
                'n_components': n_components,
                'largest_cc_ratio': largest_cc_ratio,
                'is_connected': is_connected
            }
        }
        
        # 打印基本信息
        print(f"节点数: {n_nodes}, 边数: {n_edges}")
        print(f"平均度: {avg_degree:.2f}, 度标准差: {std_degree:.2f}")
        print(f"度异质性: {degree_heterogeneity:.4f}")
        print(f"聚类系数: {avg_clustering:.4f}")
        print(f"连通分量数: {n_components}")
        
        return result
        
    except Exception as e:
        print(f"分析 {network_file} 时出错: {e}")
        return None

def main():
    networks = [
        "AS733.txt",
        "blog-int.txt", 
        "CA-GrQc-unD-int.txt",
        "CA-HepTh.txt",
        "deezer.txt",
        "lastfm.txt", 
        "NetHEHT.txt",
        "twin_zero_indexed.txt"
    ]
    
    results = []
    
    for network in networks:
        result = analyze_single_network(network)
        if result:
            results.append(result)
    
    # 异质性排名
    print("\n" + "="*60)
    print("网络异质性排名（基于度异质性指标）")
    print("="*60)
    
    # 按度异质性排序
    sorted_by_heterogeneity = sorted(results, key=lambda x: x['heterogeneity']['degree_heterogeneity'], reverse=True)
    
    for i, result in enumerate(sorted_by_heterogeneity, 1):
        network = result['network_file']
        het = result['heterogeneity']['degree_heterogeneity']
        gini = result['heterogeneity']['gini_coefficient']
        print(f"{i}. {network}: 度异质性={het:.4f}, 基尼系数={gini:.4f}")
    
    return results

if __name__ == "__main__":
    results = main()
