import networkx as nx
import numpy as np
import common as cm

def quick_network_analysis():
    """快速分析所有网络的基本特征"""
    
    networks = [
        ("AS733.txt", "自治系统网络"),
        ("blog-int.txt", "博客网络"), 
        ("CA-GrQc-unD-int.txt", "科研合作网络-广义相对论"),
        ("CA-HepTh.txt", "科研合作网络-高能物理理论"),
        ("deezer.txt", "音乐社交网络"),
        ("lastfm.txt", "音乐推荐网络"), 
        ("NetHEHT.txt", "高能物理实验网络"),
        ("twin_zero_indexed.txt", "双胞胎网络")
    ]
    
    results = {}
    
    for network_file, description in networks:
        try:
            print(f"分析 {network_file}...")
            
            # 读取文件第一行获取基本信息
            with open(f"networks/{network_file}", 'r') as f:
                first_line = f.readline().strip().split()
                n_nodes = int(first_line[0])
                n_edges = int(first_line[1])
            
            # 加载网络进行快速分析
            G = nx.Graph()
            edges = cm.get_txt_graph(filepath=f"networks/{network_file}")
            G.add_edges_from(edges)
            
            # 基本统计
            actual_nodes = G.number_of_nodes()
            actual_edges = G.number_of_edges()
            avg_degree = 2 * actual_edges / actual_nodes if actual_nodes > 0 else 0
            density = nx.density(G)
            
            # 度分布
            degrees = [d for n, d in G.degree()]
            max_degree = max(degrees) if degrees else 0
            std_degree = np.std(degrees) if degrees else 0
            degree_heterogeneity = std_degree / avg_degree if avg_degree > 0 else 0
            
            # 连通性
            is_connected = nx.is_connected(G)
            n_components = nx.number_connected_components(G)
            
            # 聚类系数（采样计算以加速）
            if actual_nodes > 1000:
                sample_nodes = np.random.choice(list(G.nodes()), min(1000, actual_nodes), replace=False)
                clustering_sample = [nx.clustering(G, node) for node in sample_nodes]
                avg_clustering = np.mean(clustering_sample)
            else:
                avg_clustering = nx.average_clustering(G)
            
            results[network_file] = {
                'description': description,
                'nodes': actual_nodes,
                'edges': actual_edges,
                'avg_degree': avg_degree,
                'max_degree': max_degree,
                'degree_heterogeneity': degree_heterogeneity,
                'density': density,
                'avg_clustering': avg_clustering,
                'is_connected': is_connected,
                'n_components': n_components
            }
            
            print(f"  节点: {actual_nodes}, 边: {actual_edges}, 度异质性: {degree_heterogeneity:.4f}")
            
        except Exception as e:
            print(f"分析 {network_file} 失败: {e}")
            continue
    
    return results

if __name__ == "__main__":
    results = quick_network_analysis()
    
    print("\n" + "="*60)
    print("网络异质性排名")
    print("="*60)
    
    # 按度异质性排序
    sorted_networks = sorted(results.items(), key=lambda x: x[1]['degree_heterogeneity'], reverse=True)
    
    for i, (network, data) in enumerate(sorted_networks, 1):
        print(f"{i}. {network} ({data['description']})")
        print(f"   度异质性: {data['degree_heterogeneity']:.4f}")
        print(f"   聚类系数: {data['avg_clustering']:.4f}")
        print(f"   密度: {data['density']:.6f}")
        print()
    
    # 保存结果
    import json
    with open('network_quick_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
